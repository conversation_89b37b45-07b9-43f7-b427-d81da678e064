# 损耗单删除问题诊断

## 🔍 问题现象

**删除操作**：
- 请求：`DELETE /mes-control-business/product/product-order-loss`
- 参数：`[14]`
- 响应：`{"code":200,"data":null,"msg":"成功","ok":true}`

**查询结果**：
- ID为14的损耗单仍然出现在分页查询结果中
- 状态：`"status": 0`（待审核）
- 明细：`"list": []`（明细已被删除）

## 🔧 问题分析

### 1. 删除逻辑检查

**Controller删除方法**：
```java
@DeleteMapping
@Transactional(rollbackFor = Exception.class)
public Boolean delete(@RequestBody List<Long> productOrderLossIds) {
    for (Long id : productOrderLossIds) {
        // 检查是否可以删除
        if (!productOrderLossService.canDelete(id)) {
            throw new RuntimeException(ProductOrderLossConstant.ErrorMessage.LOSS_CANNOT_DELETE);
        }
        
        // 删除明细
        productOrderLossDetailService.deleteByLossId(id);
        
        // 删除主表（逻辑删除）
        ProductOrderLoss loss = productOrderLossService.getById(id);
        if (loss != null) {
            loss.setFlag(ProductOrderLossConstant.Flag.DELETED); // 设置为"Y"
            loss.setUpdateBy(getCurrentUsername());
            loss.setUpdateTime(LocalDateTime.now());
            productOrderLossService.updateById(loss);
        }
    }
    return true;
}
```

**canDelete检查**：
```java
public boolean canDelete(Long productOrderLossId) {
    ProductOrderLoss loss = getById(productOrderLossId);
    if (loss == null) {
        return false;
    }
    // 待审核和审核未通过状态的损耗单可以删除
    return ProductOrderLossConstant.Status.PENDING.equals(loss.getStatus())
        || ProductOrderLossConstant.Status.REJECTED.equals(loss.getStatus());
}
```

### 2. 查询逻辑检查

**分页查询过滤条件**：
```java
private LambdaQueryWrapper<ProductOrderLoss> buildQueryWrapper(ProductOrderLossQuery query) {
    LambdaQueryWrapper<ProductOrderLoss> queryWrapper = new LambdaQueryWrapper<>();
    
    queryWrapper.eq(ProductOrderLoss::getFlag, ProductOrderLossConstant.Flag.NOT_DELETED) // 过滤已删除记录
            .eq(query.getProductOrderLossId() != null, ProductOrderLoss::getProductOrderLossId, query.getProductOrderLossId())
            // ... 其他条件
            .orderByDesc(ProductOrderLoss::getCreateTime);
    
    return queryWrapper;
}
```

### 3. 常量值检查

```java
public static class Flag {
    /** 未删除 */
    public static final String NOT_DELETED = "N";
    /** 已删除 */
    public static final String DELETED = "Y";
}
```

## 🎯 可能的原因

### 原因1：事务回滚
- 删除操作可能因为某种原因导致事务回滚
- 虽然返回成功，但实际数据没有更新

### 原因2：数据库连接问题
- 可能存在数据库连接或事务提交问题
- 更新操作没有真正写入数据库

### 原因3：并发问题
- 可能存在并发操作导致的数据不一致

### 原因4：缓存问题
- MyBatis Plus的缓存可能导致查询结果不是最新的

## 🔍 诊断步骤

### 步骤1：检查数据库实际状态
```sql
-- 直接查询数据库中ID为14的记录
SELECT product_order_loss_id, flag, status, update_by, update_time 
FROM product_order_loss 
WHERE product_order_loss_id = 14;

-- 查询所有记录的flag状态
SELECT product_order_loss_id, flag, status 
FROM product_order_loss 
ORDER BY product_order_loss_id;
```

### 步骤2：检查明细删除情况
```sql
-- 查询ID为14的损耗单的明细记录
SELECT * FROM product_order_loss_detail 
WHERE product_order_loss_id = 14;
```

### 步骤3：添加详细日志
在删除方法中添加更详细的日志：

```java
@DeleteMapping
@Transactional(rollbackFor = Exception.class)
public Boolean delete(@RequestBody List<Long> productOrderLossIds) {
    log.info("开始删除损耗单，IDs: {}", productOrderLossIds);
    
    for (Long id : productOrderLossIds) {
        log.info("检查损耗单是否可以删除，ID: {}", id);
        
        // 检查是否可以删除
        if (!productOrderLossService.canDelete(id)) {
            log.error("损耗单不能删除，ID: {}", id);
            throw new RuntimeException(ProductOrderLossConstant.ErrorMessage.LOSS_CANNOT_DELETE);
        }
        
        log.info("开始删除损耗单明细，损耗单ID: {}", id);
        // 删除明细
        boolean detailDeleted = productOrderLossDetailService.deleteByLossId(id);
        log.info("损耗单明细删除结果，损耗单ID: {}, 结果: {}", id, detailDeleted);
        
        // 删除主表（逻辑删除）
        ProductOrderLoss loss = productOrderLossService.getById(id);
        if (loss != null) {
            log.info("开始逻辑删除损耗单主表，ID: {}, 当前flag: {}", id, loss.getFlag());
            
            loss.setFlag(ProductOrderLossConstant.Flag.DELETED);
            loss.setUpdateBy(getCurrentUsername());
            loss.setUpdateTime(LocalDateTime.now());
            
            boolean updated = productOrderLossService.updateById(loss);
            log.info("损耗单主表逻辑删除结果，ID: {}, 结果: {}, 新flag: {}", 
                id, updated, ProductOrderLossConstant.Flag.DELETED);
        } else {
            log.warn("损耗单不存在，ID: {}", id);
        }
    }
    
    log.info("损耗单删除操作完成，IDs: {}", productOrderLossIds);
    return true;
}
```

## 🛠️ 解决方案

### 方案1：强制刷新缓存
在删除操作后添加缓存清理：

```java
// 删除操作完成后
productOrderLossService.getBaseMapper().selectById(id); // 强制刷新
```

### 方案2：检查事务配置
确保事务配置正确：

```java
@DeleteMapping
@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
public Boolean delete(@RequestBody List<Long> productOrderLossIds) {
    // ... 删除逻辑
}
```

### 方案3：添加删除验证
在删除操作后验证结果：

```java
// 删除主表后验证
ProductOrderLoss verifyLoss = productOrderLossService.getById(id);
if (verifyLoss != null && !ProductOrderLossConstant.Flag.DELETED.equals(verifyLoss.getFlag())) {
    log.error("删除验证失败，损耗单ID: {}, 当前flag: {}", id, verifyLoss.getFlag());
    throw new RuntimeException("删除操作失败");
}
```

## 📋 立即行动项

1. **检查数据库实际状态**：直接查询数据库确认ID为14的记录的flag值
2. **添加详细日志**：在删除方法中添加更多日志输出
3. **验证事务提交**：确保事务正确提交
4. **测试删除功能**：使用其他待审核状态的损耗单测试删除功能

## 🔄 预期结果

正常情况下：
- 删除操作后，数据库中的flag字段应该从"N"变为"Y"
- 分页查询应该过滤掉flag="Y"的记录
- 明细记录应该被物理删除

如果问题持续存在，建议：
1. 检查数据库事务隔离级别
2. 检查MyBatis Plus配置
3. 考虑使用物理删除替代逻辑删除（如果业务允许）
