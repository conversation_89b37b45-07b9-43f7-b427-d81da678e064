# 入库业务流程接口文档

## 1. 业务流程概述
入库业务流程包含以下四个主要步骤：
1. 创建入库单
2. 创建入库明细
3. 创建入库对照（物料上架）
4. 修改入库物料的质检状态

## 2. 第一步：创建入库单接口 (/mes-control-business/store/enter-storage POST)

### 2.1 接受的参数
接口接受`EnterStorageDTO`对象作为请求体，包含以下字段：
- `enterStorageId`: 入库单ID（创建时通常为空）
- `enterStorageCode`: 入库单流水号（系统自动生成）
- `referenceCode`: 引用单号（通过出库单自动创建的入库单才有）
- `status`: 入库单状态（必填，0.正操作 1.待收货 2.待质检 3.入库中 4.已入库 5.已导入）
- `documentType`: 单据类型（必填，0.仓库入库单 1.物料室入库单 2.转料入库单）
- `documentCode`: 入库单据号
- `buyType`: 采购类型（0.普通采购 1.保税采购 2.委外加工 3.保税委外）
- `supplierId`: 供应商ID
- `supplierCode`: 供应商编码
- `supplierName`: 供应商名称
- `storeId`: 仓库ID
- `storeName`: 仓库名称
- `deliveryNumber`: 送货单号
- `transportType`: 运输方式
- `enterDate`: 入库日期
- `planBoxNum`: 预计入库箱数
- `planPieceNum`: 预计入库件数
- `actualBoxNum`: 实际入库箱数（创建时通常为0）
- `actualPieceNum`: 实际入库件数（创建时通常为0）
- `money`: 金额
- `exchangeRate`: 汇率
- `taxRate`: 税率
- `enterType`: 入库方式（0.单箱入库 1.卡板入库）
- `remark`: 备注
- `createBy`: 创建人（系统自动填充）
- `createTime`: 创建时间（系统自动填充）
- `updateBy`: 修改人（系统自动填充）
- `updateTime`: 修改时间（系统自动填充）

### 2.2 返回的结果
接口返回`EnterStorageDTO`对象，包含创建成功的入库单信息，与传入参数基本一致，但会包含系统生成的字段如：
- `enterStorageId`: 新生成的入库单ID
- `enterStorageCode`: 系统生成的入库单流水号
- `createBy`: 创建人
- `createTime`: 创建时间

### 2.3 中间操作逻辑
1. 参数处理：从请求体中获取`EnterStorageDTO`对象
2. 流水号生成：调用编码规则服务`encodeRuleClient.getSerialNumber()`生成入库单流水号
3. 用户信息填充：从上下文`AuthUserContext`中获取当前用户信息，填充`createBy`字段
4. 数据保存：将`EnterStorageDTO`转换为`EnterStorage`实体对象，调用`enterStorageService.save()`方法保存入库单信息到数据库
5. 返回结果：将保存成功的实体对象转换回`EnterStorageDTO`并返回

## 3. 第二步：创建入库明细接口 (/mes-control-business/store/enter-detail/ POST)

### 3.1 接受的参数
接口接受`EnterDetailDTO`对象作为请求体，包含以下字段：
- `enterDetailId`: 入库明细ID（创建时通常为空）
- `enterStorageId`: 入库单ID（必填）
- `status`: 质检状态（0.未质检 1.质检合格 2.质检不合格）
- `materialCode`: 物料编码
- `materialId`: 物料ID
- `materialName`: 物料名称
- `boxPieceNum`: 单箱件数（必填）
- `boxNum`: 箱数（必填）
- `pieceNum`: 件数（系统计算：箱数*单箱件数）
- `unitPrice`: 单价（必填）
- `amount`: 总金额（系统计算：件数*单价）
- `planBoxNum`: 实入箱数
- `planPieceNum`: 实入件数
- `iqcResult`: IQC结果
- `checkBy`: 质检人
- `checkTime`: 质检时间
- `remark`: 备注
- `createBy`: 创建人（系统自动填充）
- `createTime`: 创建时间（系统自动填充）
- `updateBy`: 修改人（系统自动填充）
- `updateTime`: 修改时间（系统自动填充）

### 3.2 返回的结果
接口返回`EnterDetailDTO`对象，包含创建成功的入库明细信息，与传入参数基本一致，但会包含系统生成的字段如：
- `enterDetailId`: 新生成的入库明细ID
- `pieceNum`: 系统计算的件数（箱数*单箱件数）
- `amount`: 系统计算的总金额（件数*单价）
- `createBy`: 创建人
- `createTime`: 创建时间

### 3.3 中间操作逻辑
1. 参数处理：从请求体中获取`EnterDetailDTO`对象
2. 用户信息填充：从上下文`AuthUserContext`中获取当前用户信息，填充`createBy`字段
3. 数据计算：
   - 计算入库总件数 = 入库箱数 * 单箱件数
   - 计算总金额 = 件数 * 单价
4. 数据保存：
   - 将`EnterDetailDTO`转换为`EnterDetail`实体对象
   - 调用`enterDetailService.save()`方法保存入库明细信息到数据库
5. 主表数据更新：
   - 调用`updateMainData()`方法更新主表（入库单）的预计入库箱数、件数和总金额
6. 返回结果：将保存成功的实体对象转换回`EnterDetailDTO`并返回

### 3.4 updateMainData方法逻辑
该方法用于更新主表（入库单）的相关统计数据：
1. 查询该入库单下的所有入库明细
2. 累加计算总箱数、总件数和总金额
3. 更新入库单的以下字段：
   - `planBoxNum`: 预计入库箱数
   - `planPieceNum`: 预计入库件数
   - `money`: 总金额
   - 根据参数决定是否更新状态为"已导入"

## 4. 第三步：创建入库对照接口 (/mes-control-business/store/enter-contrast POST)

### 4.1 接受的参数
接口接受`EnterContrastDTO`对象作为请求体，包含以下字段：
- `enterContrastId`: 入库明细对照ID（创建时通常为空）
- `enterDetailId`: 入库明细ID
- `enterStorageId`: 入库单ID（必填）
- `materialId`: 物料ID
- `materialCode`: 物料编码
- `materialName`: 物料名称
- `materialSpec`: 物料规格
- `qrCode`: 二维码信息（必填）
- `supplierName`: 供应商名称
- `supplierCode`: 供应商编码
- `orderCode`: 订单编码
- `materialNumber`: 材质料号
- `batchNumber`: 系统批号
- `packageSerialNo`: 包装流水码
- `enterDate`: 入库时间
- `enterLot`: 入库批次
- `productDate`: 生产日期
- `expirationDate`: 有效日期
- `sendDate`: 发货日期
- `outDate`: 出库日期
- `boxNumber`: 入库箱号
- `pieceNum`: 单箱件数
- `placeLocation`: 货位位置
- `placeNum`: 货位物料数量
- `placeStatus`: 物料-货位状态（0.未上架 1.已上架 2.已下架）
- `checkStatus`: 质检状态（0.未质检 1.质检合格 2.质检不合格）
- `checkDate`: 质检日期
- `checkBy`: 质检人
- `remark`: 备注
- `createBy`: 创建人（系统自动填充）
- `createTime`: 创建时间（系统自动填充）
- `updateBy`: 修改人（系统自动填充）
- `updateTime`: 修改时间（系统自动填充）

### 4.2 返回的结果
接口返回`EnterContrastDTO`对象，包含创建成功的入库对照信息，与传入参数基本一致，但会包含系统生成的字段如：
- `enterContrastId`: 新生成的入库对照ID
- `createBy`: 创建人
- `createTime`: 创建时间

### 4.3 中间操作逻辑
1. 参数处理：从请求体中获取`EnterContrastDTO`对象
2. 数据验证：
   - 验证入库单是否存在
   - 验证货位信息（物料室类型的入库单会自动分配货位）
   - 检查货位是否被锁定
3. 二维码解析：
   - 根据编码规则解析二维码信息
   - 提取物料编码、生产日期、有效日期、发货日期、入库批次、物料规格、单箱件数等信息
4. 数据验证：
   - 验证货位对应的物料类型是否一致
   - 检查箱号是否已上架
5. 数据保存：
   - 保存入库对照信息到`enter_contrast`表
   - 保存物料货位信息到`material_place`表
6. 数据更新：
   - 更新入库明细表的实入件数和箱数
   - 更新入库单表的实入件数和箱数及状态
   - 维护物料类型与货位的映射关系
7. 返回结果：将保存成功的实体对象转换回`EnterContrastDTO`并返回

## 5. 第四步：修改入库物料的质检状态接口 (/mes-control-business/store/enter-detail/update-check-status PUT)

### 5.1 接受的参数
接口接受`List<EnterDetailDTO>`对象列表作为请求体，每个对象包含以下字段：
- `enterDetailId`: 入库明细ID（必填）
- `status`: 质检状态（0.未质检 1.质检合格 2.质检不合格）
- `checkBy`: 质检人（系统自动填充）
- `checkTime`: 质检时间（系统自动填充）

### 5.2 返回的结果
接口返回`Boolean`值，表示操作是否成功。

### 5.3 中间操作逻辑
1. 参数处理：从请求体中获取`EnterDetailDTO`对象列表
2. 用户信息填充：从上下文`AuthUserContext`中获取当前用户信息，填充`checkBy`字段
3. 时间填充：设置当前时间作为质检时间
4. 数据更新：
   - 更新入库明细对照表的质检状态、质检人和质检时间
   - 批量更新入库明细表的质检状态、质检人和质检时间
5. 状态检查：
   - 检查该入库单下的所有入库明细是否都已完成质检
   - 如果全部完成质检，则更新入库单状态为"已入库"
6. 返回结果：返回操作成功标识

## 6. 退料入库接口（已实现）
退料入库接口已实现，用于处理退料业务。该接口需要一次性完成创建入库单和创建入库明细两个步骤，体现入库单（一级）和入库明细（二级）的层级关系。

接口路径：`POST /mes-control-business/store/enter-detail/return-material`

### 6.1 接口参数
根据业务需求，退料入库接口应接受一个统一的接口请求参数结构，使用list来包含入库明细信息：

```json
{
  // 入库单信息（一级）
  "documentType": "3",        // 单据类型，定死为"3"（退料入库单）
  "enterType": "0",           // 入库方式，定死为"0"（单箱入库）
  "remark": "备注信息",        // 备注
  "status": 1,                // 入库单状态，定为1（待收货）
  "storeId": 10,              // 仓库ID
  "storeName": "原材仓",       // 仓库名
  
  // 入库明细列表（二级）
  "list": [                   // 入库明细信息列表
    {
      "boxNum": 2,            // 箱数（退料箱数）
      "pieceNum": 5000,       // 件数（总退料件数，直接设置，不计算）
      "materialId": 11,       // 物料ID
      "materialCode": "YEA0068-075V2",    // 物料代码
      "materialName": "反射(卷材)",       // 物料名
    }
    // 可以有多个明细项
  ]
}
```

### 6.2 返回结果
接口返回`EnterStorageDTO`对象，包含创建成功的入库单信息，系统会自动生成以下字段：
- `enterStorageId`: 新生成的入库单ID
- `enterStorageCode`: 系统生成的入库单流水号
- `createBy`: 创建人
- `createTime`: 创建时间

### 6.2 接口逻辑
退料入库接口的处理逻辑应包括：
1. 创建入库单：
   - 设置单据类型为退料入库单
   - 设置入库方式为单箱入库
   - 填充仓库信息
   - 设置状态为待收货
   - 其他必要信息填充

2. 创建入库明细列表：
   - 使用创建的入库单ID
   - 为每个明细项填充物料信息
   - 设置箱数和件数
   - 不计算单价和金额（退料单不需要）

3. 更新产线在工数量：
   - 当入库单状态变更为"已入库"时，如果入库单类型为退料入库单，则需要更新产线在工数量
   - 从入库单的备注字段中解析产线ID，格式为"产线ID-产线名称退料单：退料原因"，例如"2-2线退料单：123"
   - 遍历入库明细，根据解析出的产线ID和物料ID获取产线在工数量记录
   - 计算新的在工数量，扣除退料数量
   - 更新产线在工数量记录

### 6.3 特殊处理
- 退料入库不需要计算单价和金额
- 退料入库不需要供应商信息
- 退料入库的件数直接由前端传递，不通过单箱件数计算

## 7. 总体业务用途
1. 创建入库单：为仓储管理系统创建新的入库单，支持不同类型的入库单和不同状态的入库单
2. 创建入库明细：为已创建的入库单添加具体的物料明细信息，系统会自动计算相关统计数据并更新主表信息
3. 创建入库对照：实现物料的上架操作，将物料与货位进行关联，并更新相关的统计数据
4. 修改质检状态：对已上架的物料进行质检状态更新，并在所有物料质检完成后更新入库单状态
5. 退料入库：处理产线退料业务，一次性创建退料入库单和对应的入库明细
