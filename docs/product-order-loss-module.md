# 生产工单损耗管理模块

## 概述

生产工单损耗管理模块是MES系统中用于管理生产过程中物料损耗的功能模块。该模块支持损耗单的创建、审核和查询，包含主表（损耗单）和明细表（损耗明细）两个核心数据表。

## 功能特性

### 核心功能
- ✅ 损耗单创建和管理
- ✅ 损耗明细记录
- ✅ 审核流程管理
- ✅ 多维度查询和筛选
- ✅ 权限控制

### 业务流程
1. **创建损耗单** - 用户填写生产工单损耗信息并提交
2. **系统处理** - 创建损耗单主表记录和对应的明细表记录
3. **审核流程** - 损耗单进入审核流程（待审核 → 审核通过/审核未通过）
4. **权限控制** - 只有待审核状态的损耗单可以修改或删除

## 数据库表结构

### 主表：product_order_loss
```sql
-- 生产工单损耗单主表
CREATE TABLE `product_order_loss` (
  `product_order_loss_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '生产工单损耗单ID',
  `product_order_loss_code` varchar(50) NOT NULL COMMENT '生产工单损耗单流水号',
  `product_line_id` bigint(20) NOT NULL COMMENT '产线ID',
  `product_line_name` varchar(100) NOT NULL COMMENT '产线名称',
  `product_order_id` bigint(20) NOT NULL COMMENT '生产工单ID',
  `product_order_code` varchar(50) NOT NULL COMMENT '生产工单编码',
  `product_order_name` varchar(200) NOT NULL COMMENT '生产工单名称',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '审核状态（0.待审核 1.审核通过 2.审核未通过）',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `check_by` varchar(50) DEFAULT NULL COMMENT '审核人',
  `check_time` datetime DEFAULT NULL COMMENT '审核时间',
  `flag` char(1) NOT NULL DEFAULT 'N' COMMENT '逻辑删除（Y是 N否）',
  PRIMARY KEY (`product_order_loss_id`)
);
```

### 明细表：product_order_loss_detail
```sql
-- 生产工单损耗明细表
CREATE TABLE `product_order_loss_detail` (
  `product_order_loss_detail_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '生产工单损耗明细ID',
  `product_order_loss_id` bigint(20) NOT NULL COMMENT '生产工单损耗单ID',
  `material_id` bigint(20) NOT NULL COMMENT '物料ID',
  `material_code` varchar(50) NOT NULL COMMENT '物料编码',
  `material_name` varchar(200) NOT NULL COMMENT '物料名称',
  `loss_num` decimal(15,4) NOT NULL DEFAULT '0.0000' COMMENT '损耗数量',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `flag` char(1) NOT NULL DEFAULT 'N' COMMENT '逻辑删除（Y是 N否）',
  PRIMARY KEY (`product_order_loss_detail_id`)
);
```

## API接口

### 损耗单主表接口

#### 创建损耗单
```http
POST /mes-control-business/product/product-order-loss
Content-Type: application/json
```

**请求参数：**
```json
{
  "productLineId": 2,
  "productLineName": "2线",
  "productOrderCode": "PO25072548000005",
  "productOrderId": 13,
  "productOrderName": "221",
  "remark": "损耗原因说明",
  "list": [
    {
      "lossNum": 1000,
      "materialCode": "YEA0068-075V2",
      "materialId": 11,
      "materialName": "反射(卷材)",
      "remark": "明细备注"
    }
  ]
}
```

**响应数据：**
```json
{
  "productOrderLossId": 10,
  "productOrderLossCode": "LOSS20250729000012",
  "productLineId": 2,
  "productLineName": "2线",
  "productOrderId": 13,
  "productOrderCode": "PO25072548000005",
  "productOrderName": "221",
  "status": 0,
  "remark": "损耗原因说明",
  "createBy": "system",
  "createTime": "2025-07-29 08:31:46",
  "checkRemark": null,
  "list": [
    {
      "productOrderLossDetailId": 15,
      "productOrderLossId": 10,
      "materialId": 11,
      "materialCode": "YEA0068-075V2",
      "materialName": "反射(卷材)",
      "lossNum": 1000,
      "remark": "明细备注",
      "createBy": "system",
      "createTime": "2025-07-29 08:31:46"
    }
  ]
}
```

#### 获取损耗单详情
```http
GET /mes-control-business/product/product-order-loss?productOrderLossId=1
```

#### 更新损耗单
```http
PUT /mes-control-business/product/product-order-loss
Content-Type: application/json
```

**请求参数：**
```json
{
  "productOrderLossId": 10,
  "productOrderId": 13,
  "productOrderName": "测试工单-修改后",
  "productOrderCode": "PO25072548000005",
  "productLineId": 2,
  "productLineName": "2线",
  "remark": "根据审核意见调整损耗数量",
  "list": [
    {
      "lossNum": 800,
      "materialId": 11,
      "materialCode": "YEA0068-075V2",
      "materialName": "反射(卷材)"
    }
  ]
}
```

**业务逻辑：**
- 只有待审核(0)和审核未通过(2)状态的损耗单可以修改
- 如果是审核未通过状态，修改完成后自动重新提交审核（状态变为待审核）
- 审核历史记录会自动添加"修改后重新提交审核"的记录

**响应数据：**
```json
true  // 更新成功
```

**错误响应：**
```json
{
  "error": "损耗单当前状态不允许修改，只有待审核和审核未通过的损耗单可以修改"
}
```

#### 删除损耗单
```http
DELETE /mes-control-business/product/product-order-loss
Content-Type: application/json
```

```json
[1, 2, 3]  // 损耗单ID数组
```

#### 分页查询
```http
POST /mes-control-business/product/product-order-loss/page
Content-Type: application/json
```

```json
{
  "current": 1,
  "size": 10,
  "productOrderId": 13,
  "status": 0
}
```

#### 审核损耗单
```http
POST /mes-control-business/product/product-order-loss/approve
Content-Type: application/x-www-form-urlencoded
```

**请求参数：**
- `productOrderLossId`: 损耗单ID
- `status`: 审核状态（1=审核通过，2=审核未通过）
- `checkRemark`: 审核原因

**示例：**
```
productOrderLossId=1&status=1&checkRemark=审核通过，数据无误
```

**响应：**
```json
true  // 审核成功
```



#### 批量审核
```http
POST /mes-control-business/product/product-order-loss/batch-approve
Content-Type: application/x-www-form-urlencoded
```

**请求参数：**
- `productOrderLossIds`: 损耗单ID列表
- `status`: 审核状态（1=审核通过，2=审核未通过）
- `checkRemark`: 审核原因

**示例：**
```
productOrderLossIds=1,2,3&status=1&checkRemark=批量审核通过
```

### 损耗明细接口

#### 根据损耗单ID查询明细
```http
GET /mes-control-business/product/product-order-loss-detail/by-loss-id?productOrderLossId=1
```

#### 根据物料ID查询损耗明细
```http
GET /mes-control-business/product/product-order-loss-detail/by-material-id?materialId=11
```

#### 根据生产工单ID查询损耗明细
```http
GET /mes-control-business/product/product-order-loss-detail/by-product-order-id?productOrderId=13
```

#### 统计指定物料的损耗总数量
```http
GET /mes-control-business/product/product-order-loss-detail/sum-loss-num?materialId=11
```

## 状态说明

### 审核状态
- `0` - 待审核：新创建的损耗单默认状态
- `1` - 审核通过：审核人确认通过
- `2` - 审核未通过：审核人驳回，可修改后重新提交

### 权限控制
- 只有**待审核、审核未通过**状态的损耗单可以修改
- 只有**待审核、审核未通过**状态的损耗单可以删除
- 已审核的损耗单不允许修改或删除

## 部署说明

### 1. 数据库初始化
执行 `sql/product_order_loss_tables.sql` 中的DDL语句创建表结构。

### 2. 编码规则配置（必须配置）
**重要：编码规则配置是必须的！**

执行 `sql/product_order_loss_encode_rule_config.sql` 文件来配置编码规则：

```bash
# 在数据库中执行编码规则配置
mysql -u username -p database_name < sql/product_order_loss_encode_rule_config.sql
```

配置内容：
- 表名：`product_order_loss`
- 字段名：`product_order_loss_code`
- 编码格式：`LOSS + YYYYMMDD + 6位流水号`
- 示例：`LOSS20250729000001`

**如果不配置编码规则，系统在创建损耗单时会调用失败，导致无法正常创建损耗单。**

### 3. 依赖检查
确保以下依赖已正确配置：
- `mes-basic-mybatis-plus` - 包含PageUtils
- `mes-basic-web` - 包含AuthUserContext
- `mes-module-third-api` - 包含EncodeRuleClient

## 使用示例

### 前端数据格式
```javascript
// 创建损耗单的请求数据
const lossData = {
  productLineId: 2,
  productLineName: "2线",
  productOrderCode: "PO25072548000005",
  productOrderId: 13,
  productOrderName: "221",
  remark: "生产过程中的正常损耗",
  list: [
    {
      lossNum: 1000,
      materialCode: "YEA0068-075V2",
      materialId: 11,
      materialName: "反射(卷材)",
      remark: "切割损耗"
    }
  ]
};
```

### 查询参数示例
```javascript
// 分页查询参数
const queryParams = {
  current: 1,
  size: 10,
  productOrderId: 13,        // 按生产工单筛选
  status: 0,                 // 按审核状态筛选
  createStartTime: "2025-07-01 00:00:00",
  createEndTime: "2025-07-31 23:59:59"
};
```

## 注意事项

1. **事务处理**：创建和更新损耗单时，主表和明细表的操作在同一事务中执行
2. **数据验证**：创建损耗单时会验证必填字段和数据有效性
3. **编码生成**：损耗单编码由系统自动生成，格式为 LOSS + 日期 + 流水号
4. **权限控制**：基于审核状态的操作权限控制，确保数据安全

## 扩展功能

该模块设计具有良好的扩展性，后续可以添加：
- 损耗统计报表
- 损耗趋势分析
- 损耗预警机制
- 与库存系统的集成
- 损耗成本分析（需要重新添加金额相关字段）
