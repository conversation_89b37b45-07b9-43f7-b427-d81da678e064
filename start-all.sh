#!/bin/bash

# MES系统一键启动脚本
echo "开始启动MES系统所有服务..."

# 定义服务列表和对应的端口
services=(
    "mes-modules/mes-module-redis/mes-module-redis-service:8002:Redis模块"
    "mes-modules/mes-module-user/mes-module-user-service:8003:用户服务"
    "mes-modules/mes-module-third/mes-module-third-service:8007:第三方服务"
    "mes-modules/mes-module-store/mes-module-store-service:8008:仓储服务"
    "mes-modules/mes-module-product/mes-module-product-service:8009:产品服务"
    "mes-modules/mes-sso:8004:SSO服务"
    "mes-control-business:9002:控制业务模块"
    "mes-gateway:8080:网关模块"
    "mes-server:8050:服务器模块"
)

# 启动服务
for service in "${services[@]}"; do
    IFS=':' read -r path port name <<< "$service"
    echo "启动服务: $name (端口: $port)"
    cd "$path"
    nohup mvn spring-boot:run > /dev/null 2>&1 &
    cd - > /dev/null
    sleep 10  # 等待服务启动
done

echo "所有服务启动完成！"
