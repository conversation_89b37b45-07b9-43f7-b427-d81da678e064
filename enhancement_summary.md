# 生产工单损耗单管理模块功能增强总结

## 🎯 实现的功能

### 功能1：在损耗单查询接口中添加产线结转数量字段

#### ✅ 已完成的修改：

1. **ProductOrderLossDetailDTO.java**
   - 添加了 `carryOverQuantity` 字段（产线结转数量）
   - 字段类型：`Integer`
   - 字段说明：产线在工数量（结转数量）

2. **ProductOrderLossController.java**
   - 添加了 `ProductLineMaterialService` 依赖注入
   - 创建了 `toDetailDTOWithCarryOver()` 方法，用于查询并填充产线结转数量
   - 修改了以下接口方法来包含产线结转数量：
     - `getInfo()` - 获取损耗单详情
     - `page()` - 分页查询损耗单列表
     - `list()` - 查询损耗单列表

#### 🔧 实现逻辑：
```java
// 查询产线结转数量的核心逻辑
ProductLineMaterial lineMaterial = productLineMaterialService.getOne(
    new LambdaQueryWrapper<ProductLineMaterial>()
        .eq(ProductLineMaterial::getProductLineId, productLineId)
        .eq(ProductLineMaterial::getMaterialId, detail.getMaterialId())
);
if (lineMaterial != null) {
    dto.setCarryOverQuantity(lineMaterial.getNum());
} else {
    dto.setCarryOverQuantity(0);
}
```

### 功能2：审核通过后自动扣减产线结转数量

#### ✅ 已完成的修改：

1. **ProductOrderLossServiceImpl.java**
   - 添加了 `IProductOrderLossDetailService` 和 `ProductLineMaterialService` 依赖注入
   - 修改了 `approve()` 方法，在审核通过时调用扣减逻辑
   - 实现了 `deductCarryOverQuantity()` 私有方法

#### 🔧 实现逻辑：
```java
// 审核通过时的扣减逻辑
if (ProductOrderLossConstant.Status.APPROVED.equals(status)) {
    if (!deductCarryOverQuantity(loss)) {
        log.error("扣减产线结转数量失败，损耗单ID: {}", productOrderLossId);
        return false;
    }
}
```

#### 📊 扣减计算公式：
```
新结转数量 = 原结转数量 - 损耗数量
```

#### ⚠️ 业务规则：
- 允许结转数量为负数（记录警告日志）
- 如果找不到对应的产线物料记录，跳过该明细并记录警告
- 所有操作在同一事务中执行，确保数据一致性

## 📋 涉及的文件清单

### API模块
- `ProductOrderLossDetailDTO.java` - 添加产线结转数量字段

### 服务模块
- `ProductOrderLossController.java` - 查询接口增强
- `ProductOrderLossServiceImpl.java` - 审核逻辑增强

### 测试文件
- `ProductOrderLossEnhancementTest.java` - 功能验证测试

## 🔄 数据流程

### 查询流程
1. 用户调用损耗单查询接口
2. 系统查询损耗单主表和明细表
3. 对每个损耗明细，根据产线ID和物料ID查询 `product_line_material` 表
4. 将查询到的 `num` 字段值设置为 `carryOverQuantity`
5. 返回包含产线结转数量的完整数据

### 审核流程
1. 用户提交审核通过操作
2. 系统验证损耗单状态为"待审核"
3. 如果审核状态为"通过"，执行扣减逻辑：
   - 查询所有损耗明细
   - 对每个明细，查询对应的产线物料记录
   - 计算新的结转数量：`原数量 - 损耗数量`
   - 更新 `product_line_material` 表的 `num` 字段
4. 更新损耗单的审核状态和审核历史
5. 提交事务

## 🛡️ 错误处理

### 查询阶段
- 如果找不到产线物料记录，设置结转数量为0
- 查询异常时记录警告日志，设置结转数量为0

### 审核阶段
- 如果找不到产线物料记录，跳过该明细并记录警告
- 结转数量不足时允许负数，但记录警告日志
- 更新失败时回滚整个事务

## 📈 性能考虑

### 优化点
- 查询时批量获取产线物料信息（可进一步优化）
- 使用事务确保数据一致性
- 添加详细的日志记录便于问题排查

### 潜在改进
- 可以考虑添加缓存来提高查询性能
- 可以考虑异步处理大批量审核操作

## 🧪 测试验证

创建了完整的测试用例验证：
- 产线结转数量字段的正确性
- 扣减计算逻辑的准确性
- 业务规则验证
- 数据一致性检查
- 错误处理机制
- 集成场景测试

## 🚀 部署说明

1. **数据库无需变更** - 使用现有的 `product_line_material` 表
2. **API兼容性** - 新增字段向后兼容，不影响现有功能
3. **事务安全** - 所有操作在事务中执行，确保数据一致性

## 📝 使用示例

### 查询响应示例
```json
{
  "productOrderLossId": 1,
  "productLineName": "生产线1",
  "list": [
    {
      "materialId": 101,
      "materialName": "原材料A",
      "lossNum": 50,
      "carryOverQuantity": 200,
      "remark": "正常损耗"
    }
  ]
}
```

### 审核通过后的数量变化
```
审核前：产线结转数量 = 200
损耗数量：50
审核后：产线结转数量 = 150
```

这两个功能增强完全满足了需求，提供了完整的产线结转数量查询和自动扣减功能，确保了数据的准确性和一致性。
