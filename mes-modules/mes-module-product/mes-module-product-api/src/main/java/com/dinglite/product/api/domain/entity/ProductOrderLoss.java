package com.dinglite.product.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 生产工单损耗单主表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "ProductOrderLoss对象", description = "生产工单损耗单主表")
public class ProductOrderLoss extends Model<ProductOrderLoss> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "生产工单损耗单ID")
    @TableId(value = "product_order_loss_id", type = IdType.AUTO)
    private Long productOrderLossId;

    @ApiModelProperty(value = "生产工单损耗单流水号")
    private String productOrderLossCode;

    @ApiModelProperty(value = "产线ID")
    private Long productLineId;

    @ApiModelProperty(value = "产线名称")
    private String productLineName;

    @ApiModelProperty(value = "生产工单ID")
    private Long productOrderId;

    @ApiModelProperty(value = "生产工单编码")
    private String productOrderCode;

    @ApiModelProperty(value = "生产工单名称")
    private String productOrderName;

    @ApiModelProperty(value = "审核状态（0.待审核 1.审核通过 2.审核未通过）")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "审核人")
    private String checkBy;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime checkTime;

    @ApiModelProperty(value = "审核备注")
    private String checkRemark;

    @ApiModelProperty(value = "逻辑删除（Y是 N否）")
    @TableLogic(value = "N", delval = "Y")
    private String flag;

    @Override
    protected Serializable pkVal() {
        return this.productOrderLossId;
    }
}
