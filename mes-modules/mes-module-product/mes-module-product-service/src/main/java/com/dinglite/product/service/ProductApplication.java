package com.dinglite.product.service;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure;
import com.dinglite.common.constant.feign.RedisApiConstant;
import com.dinglite.common.constant.feign.StoreApiConstant;
import com.dinglite.common.constant.feign.ThirdApiConstant;
import com.dinglite.mybatis.config.MapperScannerConstant;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023-10-12 15:49
 */
@SpringBootApplication(exclude = {DruidDataSourceAutoConfigure.class})
@MapperScan(MapperScannerConstant.PRODUCT_SCAN)
@EnableFeignClients(basePackages = {RedisApiConstant.FEIGN_REDISSON_PACKAGE,
        ThirdApiConstant.FEIGN_PACKAGE,
        StoreApiConstant.FEIGN_PACKAGE})
public class ProductApplication {
    public static void main(String[] args) {
        SpringApplication.run(ProductApplication.class, args);
    }
}
