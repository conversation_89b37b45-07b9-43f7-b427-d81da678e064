package com.dinglite.product.service.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dinglite.common.annotation.LoadPayload;
import com.dinglite.common.domain.PageDTO;
import com.dinglite.common.global.AuthUser;
import com.dinglite.common.threadlocal.AuthUserContext;
import com.dinglite.mybatis.utils.PageUtils;
import com.dinglite.product.api.domain.constant.ProductOrderLossConstant;
import com.dinglite.product.api.domain.dto.ProductOrderLossDTO;
import com.dinglite.product.api.domain.dto.ProductOrderLossDetailDTO;
import com.dinglite.product.api.domain.entity.ProductOrderLoss;
import com.dinglite.product.api.domain.entity.ProductOrderLossDetail;
import com.dinglite.product.api.domain.entity.ProductLineMaterial;
import com.dinglite.product.api.domain.query.ProductOrderLossQuery;
import com.dinglite.product.service.service.IProductOrderLossDetailService;
import com.dinglite.product.service.service.IProductOrderLossService;
import com.dinglite.product.service.service.ProductLineMaterialService;
import com.dinglite.product.service.util.AuditHistoryUtil;
import com.dinglite.store.api.service.OutDetailClient;
import com.dinglite.third.api.service.EncodeRuleClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 生产工单损耗单主表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/product/product-order-loss/api")
@Api(tags = "生产工单损耗单API")
public class ProductOrderLossController {

    @Autowired
    private IProductOrderLossService productOrderLossService;

    @Autowired
    private IProductOrderLossDetailService productOrderLossDetailService;

    @Autowired
    private ProductLineMaterialService productLineMaterialService;

    @Autowired
    private OutDetailClient outDetailClient;

    @Autowired
    private EncodeRuleClient encodeRuleClient;

    /**
     * 转换为DTO
     */
    public static ProductOrderLossDTO toDTO(ProductOrderLoss productOrderLoss) {
        if (productOrderLoss == null) {
            return null;
        }
        ProductOrderLossDTO dto = new ProductOrderLossDTO();
        BeanUtils.copyProperties(productOrderLoss, dto);
        return dto;
    }

    /**
     * 获取当前用户名，如果没有认证信息则返回默认值
     */
    private String getCurrentUsername() {
        try {
            AuthUser authUser = AuthUserContext.get();
            if (authUser != null) {
                String username = authUser.getUsername();
                if (StringUtils.hasText(username)) {
                    log.debug("成功获取到用户名: {}", username);
                    return username;
                } else {
                    log.warn("AuthUser存在但username为空, AuthUser: {}", authUser);
                }
            } else {
                log.warn("AuthUserContext.get()返回null，可能是认证信息未正确传递");
            }
        } catch (Exception e) {
            log.error("获取当前用户名时发生异常", e);
        }

        log.warn("无法获取到有效的用户名，使用默认值'system'");
        return "system"; // 测试环境默认用户
    }

    /**
     * 转换为明细DTO
     */
    public static ProductOrderLossDetailDTO toDetailDTO(ProductOrderLossDetail detail) {
        if (detail == null) {
            return null;
        }
        ProductOrderLossDetailDTO dto = new ProductOrderLossDetailDTO();
        BeanUtils.copyProperties(detail, dto);
        return dto;
    }

    /**
     * 转换为明细DTO（包含产线在工数量和结转预定量）
     */
    public ProductOrderLossDetailDTO toDetailDTOWithGenerateNum(ProductOrderLossDetail detail, Long productLineId, Long productOrderId) {
        if (detail == null) {
            return null;
        }
        ProductOrderLossDetailDTO dto = new ProductOrderLossDetailDTO();
        BeanUtils.copyProperties(detail, dto);

        // 查询产线在工数量
        if (productLineId != null && detail.getMaterialId() != null) {
            try {
                ProductLineMaterial lineMaterial = productLineMaterialService.getOne(
                    new LambdaQueryWrapper<ProductLineMaterial>()
                        .eq(ProductLineMaterial::getProductLineId, productLineId)
                        .eq(ProductLineMaterial::getMaterialId, detail.getMaterialId())
                );
                if (lineMaterial != null) {
                    dto.setGenerateNum(lineMaterial.getNum());
                } else {
                    dto.setGenerateNum(0);
                }
            } catch (Exception e) {
                log.warn("查询产线在工数量失败，产线ID: {}, 物料ID: {}", productLineId, detail.getMaterialId(), e);
                dto.setGenerateNum(0);
            }
        } else {
            dto.setGenerateNum(0);
        }

        // 查询结转预定量
        if (productOrderId != null && detail.getMaterialId() != null) {
            try {
                Integer generateReserveNum = outDetailClient.getGenerateReserveNum(
                    productOrderId, detail.getMaterialId());
                dto.setGenerateReserveNum(generateReserveNum != null ? generateReserveNum : 0);
                log.debug("成功查询结转预定量，工单ID: {}, 物料ID: {}, 结转预定量: {}",
                    productOrderId, detail.getMaterialId(), generateReserveNum);
            } catch (Exception e) {
                log.warn("查询结转预定量失败，工单ID: {}, 物料ID: {}", productOrderId, detail.getMaterialId(), e);
                dto.setGenerateReserveNum(0);
            }
        } else {
            dto.setGenerateReserveNum(0);
        }

        return dto;
    }

    @PostMapping
    @ApiOperation("创建生产工单损耗单")
    @LoadPayload
    @Transactional(rollbackFor = Exception.class)
    public ProductOrderLossDTO create(@Valid @RequestBody ProductOrderLossDTO dto) {
        // 生成损耗单流水号
        String serialNumber = encodeRuleClient.getSerialNumber(
                ProductOrderLossConstant.CodeRule.TABLE_NAME,
                ProductOrderLossConstant.CodeRule.FIELD_NAME);
        dto.setProductOrderLossCode(serialNumber);

        dto.setCreateBy(getCurrentUsername());
        dto.setStatus(ProductOrderLossConstant.Status.PENDING);
        dto.setCreateTime(LocalDateTime.now());

        // 保存主表
        ProductOrderLoss productOrderLoss = new ProductOrderLoss();
        BeanUtils.copyProperties(dto, productOrderLoss);
        productOrderLoss.setFlag(ProductOrderLossConstant.Flag.NOT_DELETED);
        productOrderLossService.save(productOrderLoss);
        dto.setProductOrderLossId(productOrderLoss.getProductOrderLossId());

        // 保存明细表
        if (dto.getList() != null && !dto.getList().isEmpty()) {
            List<ProductOrderLossDetail> details = dto.getList().stream().map(detailDTO -> {
                ProductOrderLossDetail detail = new ProductOrderLossDetail();
                BeanUtils.copyProperties(detailDTO, detail);
                detail.setProductOrderLossId(productOrderLoss.getProductOrderLossId());
                detail.setCreateBy(getCurrentUsername());
                detail.setCreateTime(LocalDateTime.now());
                detail.setFlag(ProductOrderLossConstant.Flag.NOT_DELETED);
                return detail;
            }).collect(Collectors.toList());

            productOrderLossDetailService.batchSave(productOrderLoss.getProductOrderLossId(), details);
        }

        return dto;
    }

    @GetMapping("/getInfo")
    @ApiOperation("获取生产工单损耗单详情")
    public ProductOrderLossDTO getInfo(@RequestParam Long productOrderLossId) {
        ProductOrderLoss productOrderLoss = productOrderLossService.getById(productOrderLossId);
        if (productOrderLoss == null) {
            return null;
        }

        ProductOrderLossDTO dto = toDTO(productOrderLoss);

        // 获取明细列表，包含产线在工数量和结转预定量
        List<ProductOrderLossDetail> details = productOrderLossDetailService.listByLossId(productOrderLossId);
        List<ProductOrderLossDetailDTO> detailDTOs = details.stream()
                .map(detail -> toDetailDTOWithGenerateNum(detail, productOrderLoss.getProductLineId(), productOrderLoss.getProductOrderId()))
                .collect(Collectors.toList());
        dto.setList(detailDTOs);

        return dto;
    }

    @PutMapping
    @ApiOperation("更新生产工单损耗单")
    @LoadPayload
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(@Valid @RequestBody ProductOrderLossDTO dto) {
        // 检查是否可以修改
        if (!productOrderLossService.canModify(dto.getProductOrderLossId())) {
            throw new RuntimeException(ProductOrderLossConstant.ErrorMessage.LOSS_CANNOT_MODIFY);
        }

        // 获取当前损耗单状态
        ProductOrderLoss currentLoss = productOrderLossService.getById(dto.getProductOrderLossId());
        if (currentLoss == null) {
            throw new RuntimeException(ProductOrderLossConstant.ErrorMessage.LOSS_NOT_FOUND);
        }

        String currentUser = getCurrentUsername();
        dto.setUpdateBy(currentUser);
        dto.setUpdateTime(LocalDateTime.now());

        // 如果是审核未通过状态，修改后自动重新提交审核（状态变为待审核）
        if (ProductOrderLossConstant.Status.REJECTED.equals(currentLoss.getStatus())) {
            dto.setStatus(ProductOrderLossConstant.Status.PENDING);
            // 添加重新提交的审核历史记录，使用用户填写的备注
            String resubmitRemark = StringUtils.hasText(dto.getRemark()) ?
                dto.getRemark() : "修改后重新提交审核";
            String updatedCheckRemark = AuditHistoryUtil.addAuditRecord(
                currentLoss.getCheckRemark(), currentUser,
                ProductOrderLossConstant.Status.REJECTED,
                ProductOrderLossConstant.Status.PENDING,
                resubmitRemark);
            dto.setCheckRemark(updatedCheckRemark);
        }

        // 更新主表
        ProductOrderLoss productOrderLoss = new ProductOrderLoss();
        BeanUtils.copyProperties(dto, productOrderLoss);
        productOrderLossService.updateById(productOrderLoss);

        // 更新明细表
        if (dto.getList() != null) {
            List<ProductOrderLossDetail> details = dto.getList().stream().map(detailDTO -> {
                ProductOrderLossDetail detail = new ProductOrderLossDetail();
                BeanUtils.copyProperties(detailDTO, detail);
                detail.setProductOrderLossId(dto.getProductOrderLossId());
                detail.setCreateBy(getCurrentUsername());
                detail.setCreateTime(LocalDateTime.now());
                detail.setUpdateBy(getCurrentUsername());
                detail.setUpdateTime(LocalDateTime.now());
                detail.setFlag(ProductOrderLossConstant.Flag.NOT_DELETED);
                return detail;
            }).collect(Collectors.toList());

            productOrderLossDetailService.batchSave(dto.getProductOrderLossId(), details);
        }

        return true;
    }

    @DeleteMapping
    @ApiOperation("删除生产工单损耗单")
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(@RequestBody List<Long> productOrderLossIds) {
        log.info("开始删除损耗单，IDs: {}", productOrderLossIds);

        for (Long id : productOrderLossIds) {
            log.info("检查损耗单是否可以删除，ID: {}", id);

            // 检查是否可以删除
            if (!productOrderLossService.canDelete(id)) {
                log.error("损耗单不能删除，ID: {}", id);
                throw new RuntimeException(ProductOrderLossConstant.ErrorMessage.LOSS_CANNOT_DELETE);
            }

            log.info("开始删除损耗单明细，损耗单ID: {}", id);
            // 删除明细
            boolean detailDeleted = productOrderLossDetailService.deleteByLossId(id);
            log.info("损耗单明细删除结果，损耗单ID: {}, 结果: {}", id, detailDeleted);

            // 删除主表（使用MyBatis Plus逻辑删除）
            ProductOrderLoss loss = productOrderLossService.getById(id);
            if (loss != null) {
                log.info("开始逻辑删除损耗单主表，ID: {}, 当前flag: {}, 当前状态: {}",
                    id, loss.getFlag(), loss.getStatus());

                // 设置更新人和更新时间（在删除前）
                loss.setUpdateBy(getCurrentUsername());
                loss.setUpdateTime(LocalDateTime.now());
                productOrderLossService.updateById(loss);

                // 使用MyBatis Plus的逻辑删除
                boolean deleted = productOrderLossService.removeById(id);
                log.info("损耗单主表逻辑删除结果，ID: {}, 删除结果: {}", id, deleted);

                if (!deleted) {
                    log.error("逻辑删除失败，损耗单ID: {}", id);
                    throw new RuntimeException("删除操作失败");
                }

                // 验证删除结果（使用原生查询，不受逻辑删除影响）
                ProductOrderLoss verifyLoss = productOrderLossService.getBaseMapper().selectById(id);
                if (verifyLoss != null) {
                    log.info("删除后验证（原生查询），ID: {}, 当前flag: {}", id, verifyLoss.getFlag());
                    if (!ProductOrderLossConstant.Flag.DELETED.equals(verifyLoss.getFlag())) {
                        log.error("删除验证失败，损耗单ID: {}, 期望flag: {}, 实际flag: {}",
                            id, ProductOrderLossConstant.Flag.DELETED, verifyLoss.getFlag());
                        throw new RuntimeException("删除操作失败，数据未正确更新");
                    }
                } else {
                    log.error("删除后验证时未找到记录，ID: {}", id);
                    throw new RuntimeException("删除操作异常，记录丢失");
                }
            } else {
                log.warn("损耗单不存在，ID: {}", id);
            }
        }

        log.info("损耗单删除操作完成，IDs: {}", productOrderLossIds);
        return true;
    }

    @PostMapping("/page")
    @ApiOperation("分页查询生产工单损耗单")
    @LoadPayload
    public PageDTO<ProductOrderLossDTO> page(@RequestBody ProductOrderLossQuery query) {
        LambdaQueryWrapper<ProductOrderLoss> queryWrapper = buildQueryWrapper(query);

        IPage<ProductOrderLoss> page = productOrderLossService.page(
                new Page<>(query.getCurrent(), query.getSize()), queryWrapper);

        // 转换为DTO并填充明细信息（包含产线在工数量）
        IPage<ProductOrderLossDTO> dtoPage = page.convert(loss -> {
            ProductOrderLossDTO dto = toDTO(loss);
            // 获取明细列表，包含产线在工数量和结转预定量
            List<ProductOrderLossDetail> details = productOrderLossDetailService.listByLossId(loss.getProductOrderLossId());
            List<ProductOrderLossDetailDTO> detailDTOs = details.stream()
                    .map(detail -> toDetailDTOWithGenerateNum(detail, loss.getProductLineId(), loss.getProductOrderId()))
                    .collect(Collectors.toList());
            dto.setList(detailDTOs);
            return dto;
        });

        return PageUtils.toDTO(dtoPage);
    }

    @PostMapping("/list")
    @ApiOperation("查询生产工单损耗单列表")
    public List<ProductOrderLossDTO> list(@RequestBody ProductOrderLossQuery query) {
        LambdaQueryWrapper<ProductOrderLoss> queryWrapper = buildQueryWrapper(query);

        return productOrderLossService.list(queryWrapper)
                .stream()
                .map(loss -> {
                    ProductOrderLossDTO dto = toDTO(loss);
                    // 获取明细列表，包含产线在工数量和结转预定量
                    List<ProductOrderLossDetail> details = productOrderLossDetailService.listByLossId(loss.getProductOrderLossId());
                    List<ProductOrderLossDetailDTO> detailDTOs = details.stream()
                            .map(detail -> toDetailDTOWithGenerateNum(detail, loss.getProductLineId(), loss.getProductOrderId()))
                            .collect(Collectors.toList());
                    dto.setList(detailDTOs);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    @PostMapping("/approve")
    @ApiOperation("审核生产工单损耗单")
    @LoadPayload
    @Transactional(rollbackFor = Exception.class)
    public Boolean approve(@RequestParam Long productOrderLossId,
                          @RequestParam Integer status,
                          @RequestParam(required = false) String checkRemark) {
        String checkBy = getCurrentUsername();
        return productOrderLossService.approve(productOrderLossId, status, checkBy, checkRemark);
    }

    @PostMapping("/batch-approve")
    @ApiOperation("批量审核生产工单损耗单")
    @LoadPayload
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchApprove(@RequestParam List<Long> productOrderLossIds,
                               @RequestParam Integer status,
                               @RequestParam(required = false) String checkRemark) {
        String checkBy = getCurrentUsername();
        return productOrderLossService.batchApprove(productOrderLossIds, status, checkBy, checkRemark);
    }



    @GetMapping("/by-product-order")
    @ApiOperation("根据生产工单ID查询损耗单列表")
    public List<ProductOrderLossDTO> listByProductOrderId(@RequestParam Long productOrderId) {
        List<ProductOrderLoss> losses = productOrderLossService.listByProductOrderId(productOrderId);
        return losses.stream()
                .map(ProductOrderLossController::toDTO)
                .collect(Collectors.toList());
    }

    @GetMapping("/by-product-line")
    @ApiOperation("根据产线ID查询损耗单列表")
    public List<ProductOrderLossDTO> listByProductLineId(@RequestParam Long productLineId) {
        List<ProductOrderLoss> losses = productOrderLossService.listByProductLineId(productLineId);
        return losses.stream()
                .map(ProductOrderLossController::toDTO)
                .collect(Collectors.toList());
    }

    @GetMapping("/by-status")
    @ApiOperation("根据审核状态查询损耗单列表")
    public List<ProductOrderLossDTO> listByStatus(@RequestParam Integer status) {
        List<ProductOrderLoss> losses = productOrderLossService.listByStatus(status);
        return losses.stream()
                .map(ProductOrderLossController::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<ProductOrderLoss> buildQueryWrapper(ProductOrderLossQuery query) {
        LambdaQueryWrapper<ProductOrderLoss> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(ProductOrderLoss::getFlag, ProductOrderLossConstant.Flag.NOT_DELETED)
                .eq(query.getProductOrderLossId() != null, ProductOrderLoss::getProductOrderLossId, query.getProductOrderLossId())
                .like(StringUtils.hasText(query.getProductOrderLossCode()), ProductOrderLoss::getProductOrderLossCode, query.getProductOrderLossCode())
                .eq(query.getProductLineId() != null, ProductOrderLoss::getProductLineId, query.getProductLineId())
                .like(StringUtils.hasText(query.getProductLineName()), ProductOrderLoss::getProductLineName, query.getProductLineName())
                .eq(query.getProductOrderId() != null, ProductOrderLoss::getProductOrderId, query.getProductOrderId())
                .like(StringUtils.hasText(query.getProductOrderCode()), ProductOrderLoss::getProductOrderCode, query.getProductOrderCode())
                .like(StringUtils.hasText(query.getProductOrderName()), ProductOrderLoss::getProductOrderName, query.getProductOrderName())
                .eq(query.getStatus() != null, ProductOrderLoss::getStatus, query.getStatus())
                .eq(StringUtils.hasText(query.getCreateBy()), ProductOrderLoss::getCreateBy, query.getCreateBy())
                .eq(StringUtils.hasText(query.getCheckBy()), ProductOrderLoss::getCheckBy, query.getCheckBy())
                .ge(StringUtils.hasText(query.getCreateStartTime()), ProductOrderLoss::getCreateTime, query.getCreateStartTime())
                .le(StringUtils.hasText(query.getCreateEndTime()), ProductOrderLoss::getCreateTime, query.getCreateEndTime())
                .ge(StringUtils.hasText(query.getCheckStartTime()), ProductOrderLoss::getCheckTime, query.getCheckStartTime())
                .le(StringUtils.hasText(query.getCheckEndTime()), ProductOrderLoss::getCheckTime, query.getCheckEndTime())
                .orderByDesc(ProductOrderLoss::getCreateTime);
        
        return queryWrapper;
    }
}
